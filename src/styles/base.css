/**
 * @description
 * This file contains base-level global styles and resets, plus any overrides
 * that apply site-wide. It includes:
 * - Root container styling for #root
 * - Basic styling for .logo and brand visuals
 * - Smooth scrolling and selection styles
 * - Scrollbar hiding utilities
 * - **Revised** global font-size overrides for better readability, especially on mobile
 *
 * @notes
 * - We have bumped the base font-size from 1.0125rem to 1.1rem.
 * - On mobile (max-width: 767px), we bumped it to 1.2rem. This should be a
 *   clearly visible jump in text size. Adjust further as desired.
 */

/* The root container: center the app and set max width */
#root {
  max-width: 1280px;
  margin: 0 auto;
  padding: 2rem;
  text-align: center;
}

.logo {
  height: 6em;
  padding: 1.5em;
  will-change: filter;
  transition: filter 300ms;
}
.logo:hover {
  filter: drop-shadow(0 0 2em #646cffaa);
}
.logo.react:hover {
  filter: drop-shadow(0 0 2em #61dafbaa);
}

/* Keyframe for spinning animation on certain logos */
@keyframes logo-spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* Only animate logos if user does not prefer reduced motion */
@media (prefers-reduced-motion: no-preference) {
  a:nth-of-type(2) .logo {
    animation: logo-spin infinite 20s linear;
  }
}

.card {
  padding: 2em;
}

.read-the-docs {
  color: #888;
}

/* Text highlight effect */
::selection {
  background: rgba(255, 215, 0, 0.2);
  color: white;
}

/* Smooth scroll behavior */
html {
  scroll-behavior: smooth;
}

/* Logo styling */
.datawise-logo {
  max-height: 40px;
  width: auto;
  transition: transform 0.3s ease;
}

.datawise-logo:hover {
  filter: drop-shadow(0 0 8px rgba(255, 215, 0, 0.5));
}

/* Hide scrollbar for Chrome, Safari, Opera */
.hide-scrollbar::-webkit-scrollbar {
  display: none;
}

/* Hide scrollbar for IE, Edge, Firefox */
.hide-scrollbar {
  -ms-overflow-style: none; /* IE and Edge */
  scrollbar-width: none;    /* Firefox */
}

/* 
  Updated global font sizes for better readability:
  - Desktop/All screens: 1.1rem
  - Mobile (max-width: 767px): 1.2rem
*/
@layer base {
  /* Slightly larger base font size across all screens. */
  body {
    font-size: 1.1rem;  /* was 1.0125rem previously */
    line-height: 1.6;
  }

  /* More noticeable increase on mobile screens. */
  @media (max-width: 767px) {
    body {
      font-size: 1.2rem; /* up from 1.05rem */
      line-height: 1.65;
    }
  }

  /* Prevent font scaling from affecting small UI elements like dots */
  button[class*="w-2 h-2"],
  button[class*="w-1.5 h-1.5"],
  button[class*="w-[0.3rem] h-[0.3rem]"],
  .navigation-dot,
  .pagination-dot {
    font-size: 0 !important;
  }
}