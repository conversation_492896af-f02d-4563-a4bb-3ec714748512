import { defineConfig } from "vite";
import react from "@vitejs/plugin-react-swc";
import { fileURLToPath, URL } from "node:url";
import { componentTagger } from "lovable-tagger";

// https://vitejs.dev/config/
export default defineConfig(({ mode }) => ({
  server: {
    host: "::",
    port: 8080,
    allowedHosts: ["596506f3-0fc3-471e-8598-a2124ea4e5f8.lovableproject.com"],
    fs: {
      allow: ['..']
    },
    // Add history API fallback for SPA routing
    proxy: {
      // This ensures that any path that doesn't match a static asset
      // will serve index.html, allowing client-side routing to work on refresh
      '/blog': {
        target: 'http://localhost:8080',
        changeOrigin: true,
        rewrite: () => '/index.html'
      },
      '/guides': {
        target: 'http://localhost:8080',
        changeOrigin: true,
        rewrite: () => '/index.html'
      }
    }
  },
  plugins: [
    react(),
    mode === 'development' &&
    componentTagger(),
  ].filter(Boolean),
  resolve: {
    alias: {
      "@": fileURLToPath(new URL('./src', import.meta.url)),
    },
  },
  assetsInclude: ['**/*.md'],
  build: {
    // Enhanced bundle splitting for optimal performance
    rollupOptions: {
      output: {
        manualChunks: (id) => {
          // Vendor chunks - separate by category for better caching
          if (id.includes('node_modules')) {
            // React ecosystem
            if (id.includes('react') || id.includes('react-dom')) {
              return 'vendor-react';
            }
            // Router
            if (id.includes('react-router')) {
              return 'vendor-router';
            }
            // UI libraries
            if (id.includes('framer-motion') || id.includes('lucide-react') ||
                id.includes('@radix-ui') || id.includes('class-variance-authority')) {
              return 'vendor-ui';
            }
            // Analytics
            if (id.includes('posthog') || id.includes('@vercel/analytics') ||
                id.includes('@vercel/speed-insights')) {
              return 'vendor-analytics';
            }
            // Forms and validation
            if (id.includes('react-hook-form') || id.includes('@hookform') ||
                id.includes('zod')) {
              return 'vendor-forms';
            }
            // Data fetching
            if (id.includes('@tanstack/react-query')) {
              return 'vendor-query';
            }
            // Charts and visualization
            if (id.includes('recharts') || id.includes('d3-')) {
              return 'vendor-charts';
            }
            // Utilities
            if (id.includes('date-fns') || id.includes('clsx') ||
                id.includes('tailwind-merge')) {
              return 'vendor-utils';
            }
            // Markdown processing
            if (id.includes('gray-matter') || id.includes('remark') ||
                id.includes('marked')) {
              return 'vendor-markdown';
            }
            // Everything else
            return 'vendor-misc';
          }

          // App chunks - separate by feature
          if (id.includes('/pages/')) {
            return 'pages';
          }
          if (id.includes('/components/performance/')) {
            return 'performance';
          }
          if (id.includes('/components/ui/')) {
            return 'ui-components';
          }
          if (id.includes('/components/error/')) {
            return 'error-handling';
          }
          if (id.includes('/hooks/')) {
            return 'hooks';
          }
          if (id.includes('/lib/')) {
            return 'lib';
          }
        },
        // Optimize chunk file names
        chunkFileNames: (chunkInfo) => {
          const facadeModuleId = chunkInfo.facadeModuleId ?
            chunkInfo.facadeModuleId.split('/').pop()?.replace('.tsx', '').replace('.ts', '') :
            'chunk';
          return `assets/[name]-[hash].js`;
        },
        entryFileNames: 'assets/[name]-[hash].js',
        assetFileNames: (assetInfo) => {
          const info = assetInfo.name?.split('.') || [];
          const ext = info[info.length - 1];
          if (/png|jpe?g|svg|gif|tiff|bmp|ico/i.test(ext || '')) {
            return 'assets/images/[name]-[hash][extname]';
          }
          if (/woff2?|eot|ttf|otf/i.test(ext || '')) {
            return 'assets/fonts/[name]-[hash][extname]';
          }
          return 'assets/[name]-[hash][extname]';
        },
      },
    },
    cssCodeSplit: true,
    sourcemap: false, // Disable in production for smaller builds
    minify: 'terser',
    terserOptions: {
      compress: {
        drop_console: true,
        drop_debugger: true,
        pure_funcs: ['console.log', 'console.info', 'console.debug'],
      },
      mangle: {
        safari10: true,
      },
    },
    // Optimize chunk size warnings
    chunkSizeWarningLimit: 1000,
    // Enable build analysis
    reportCompressedSize: true,
  },
}));
